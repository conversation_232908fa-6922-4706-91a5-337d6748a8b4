/**
 * Automatic Page Refresh Utility for User Switching
 * 
 * This utility provides different strategies for refreshing page data
 * when users switch accounts, ensuring all components show the correct user's data.
 */

import { getCurrentUserContext } from './userSwitchUtils';

/**
 * Strategy 1: Automatic Page Reload
 * Simple but effective - reloads the entire page when user switches
 */
export const enableAutoPageReload = (options = {}) => {
  const { 
    delay = 500, 
    debug = false,
    excludeRoutes = [] // Routes that should not auto-reload
  } = options;

  const handleUserSwitch = (event) => {
    const currentPath = window.location.pathname;
    
    // Check if current route should be excluded
    if (excludeRoutes.some(route => currentPath.includes(route))) {
      if (debug) {
        console.log('🚫 Auto page reload skipped for route:', currentPath);
      }
      return;
    }

    if (debug) {
      console.log('🔄 Auto page reload triggered by user switch:', event.detail);
    }

    setTimeout(() => {
      if (debug) {
        console.log('🔄 Reloading page...');
      }
      window.location.reload();
    }, delay);
  };

  // Listen for user switch events
  window.addEventListener('userSwitched', handleUserSwitch);

  // Return cleanup function
  return () => {
    window.removeEventListener('userSwitched', handleUserSwitch);
  };
};

/**
 * Strategy 2: Smart Component Refresh
 * Refreshes specific components without full page reload
 */
export const enableSmartComponentRefresh = (options = {}) => {
  const { 
    debug = false,
    refreshSelectors = [
      '[data-user-dependent]',
      '[data-refresh-on-switch]',
      '.user-data-component'
    ]
  } = options;

  const handleUserSwitch = (event) => {
    if (debug) {
      console.log('🔄 Smart component refresh triggered:', event.detail);
    }

    // Find all components that need refreshing
    refreshSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        // Trigger custom refresh event on each element
        element.dispatchEvent(new CustomEvent('refreshUserData', {
          detail: event.detail
        }));
      });
    });

    // Also trigger a global component refresh event
    window.dispatchEvent(new CustomEvent('refreshAllComponents', {
      detail: event.detail
    }));
  };

  window.addEventListener('userSwitched', handleUserSwitch);

  return () => {
    window.removeEventListener('userSwitched', handleUserSwitch);
  };
};

/**
 * Strategy 3: Route-based Refresh
 * Different refresh strategies for different routes
 */
export const enableRouteBasedRefresh = (routeConfig = {}) => {
  const defaultConfig = {
    '/dashboard': 'reload',
    '/profile': 'reload', 
    '/analytics': 'reload',
    '/planner': 'reload',
    '/chat': 'component',
    '/live': 'component'
  };

  const config = { ...defaultConfig, ...routeConfig };

  const handleUserSwitch = (event) => {
    const currentPath = window.location.pathname;
    const strategy = config[currentPath] || 'reload';

    console.log(`🔄 Route-based refresh for ${currentPath}: ${strategy}`);

    switch (strategy) {
      case 'reload':
        setTimeout(() => {
          window.location.reload();
        }, 300);
        break;
      
      case 'component':
        window.dispatchEvent(new CustomEvent('refreshAllComponents', {
          detail: event.detail
        }));
        break;
      
      case 'none':
        // Do nothing
        break;
      
      default:
        // Custom strategy function
        if (typeof strategy === 'function') {
          strategy(event.detail);
        }
    }
  };

  window.addEventListener('userSwitched', handleUserSwitch);

  return () => {
    window.removeEventListener('userSwitched', handleUserSwitch);
  };
};

/**
 * Strategy 4: Hybrid Approach
 * Combines multiple strategies for optimal user experience
 */
export const enableHybridRefresh = (options = {}) => {
  const {
    debug = false,
    reloadRoutes = ['/dashboard', '/profile', '/analytics'],
    componentRoutes = ['/chat', '/live'],
    delay = 400
  } = options;

  const handleUserSwitch = (event) => {
    const currentPath = window.location.pathname;

    if (debug) {
      console.log('🔄 Hybrid refresh for path:', currentPath, 'Event:', event.detail);
    }

    // Check if current route should reload
    if (reloadRoutes.some(route => currentPath.includes(route))) {
      setTimeout(() => {
        if (debug) {
          console.log('🔄 Reloading page for route:', currentPath);
        }
        window.location.reload();
      }, delay);
      return;
    }

    // Check if current route should use component refresh
    if (componentRoutes.some(route => currentPath.includes(route))) {
      if (debug) {
        console.log('🔄 Component refresh for route:', currentPath);
      }
      window.dispatchEvent(new CustomEvent('refreshAllComponents', {
        detail: event.detail
      }));
      return;
    }

    // Default: reload
    setTimeout(() => {
      if (debug) {
        console.log('🔄 Default reload for route:', currentPath);
      }
      window.location.reload();
    }, delay);
  };

  window.addEventListener('userSwitched', handleUserSwitch);

  return () => {
    window.removeEventListener('userSwitched', handleUserSwitch);
  };
};

/**
 * Initialize the automatic refresh system
 * Call this once in your App.jsx or main component
 */
export const initializeAutoRefresh = (strategy = 'hybrid', options = {}) => {
  console.log('🚀 Initializing auto refresh system with strategy:', strategy);

  switch (strategy) {
    case 'reload':
      return enableAutoPageReload(options);
    
    case 'component':
      return enableSmartComponentRefresh(options);
    
    case 'route':
      return enableRouteBasedRefresh(options);
    
    case 'hybrid':
    default:
      return enableHybridRefresh(options);
  }
};

/**
 * Utility to manually trigger refresh for current page
 */
export const triggerManualRefresh = (reason = 'manual') => {
  const userContext = getCurrentUserContext();
  
  window.dispatchEvent(new CustomEvent('userSwitched', {
    detail: {
      reason,
      userContext,
      timestamp: Date.now()
    }
  }));
};

export default {
  enableAutoPageReload,
  enableSmartComponentRefresh,
  enableRouteBasedRefresh,
  enableHybridRefresh,
  initializeAutoRefresh,
  triggerManualRefresh
};
