# User Switching Implementation - Instagram-like Multi-User Functionality

## Overview

This implementation provides Instagram-like multi-user functionality where:

- User A can invite and switch to other users (User B, C, etc.)
- When switching users, the switched user's brand ID is used for data context
- The original user's ID is always used for authorization
- All API calls automatically include the correct headers
- UI updates automatically when users switch

## Key Components

### 1. Enhanced Axios Interceptor (`src/helpers/Axios/axiosINstance.jsx`)

- **Purpose**: Automatically handles headers for all API calls
- **Key Features**:
  - Uses original user's token for authentication
  - Uses original user's ID for authorization (`user` header)
  - Uses switched user's brand ID for data context (`brand` header)
  - Automatic header injection for all API requests

### 2. User Context (`src/helpers/context/UserContext.jsx`)

- **Purpose**: Manages user switching state across the application
- **Key Features**:
  - Tracks original user data (never changes)
  - Tracks switched user data (changes when switching)
  - Provides current user context
  - Handles API calls for user switching

### 3. User Switch Utilities (`src/helpers/utils/userSwitchUtils.js`)

- **Purpose**: Utility functions for user switching operations
- **Key Functions**:
  - `getCurrentUserContext()`: Get current user state
  - `switchToUser(user)`: Switch to a specific user
  - `fetchAvailableUsers()`: Get list of switchable users
  - `addUserAccount(credentials)`: Add new user account

### 4. Event Listener Hooks (`src/helpers/hooks/useUserSwitchListener.js`)

- **Purpose**: React hooks for listening to user switch events
- **Key Hooks**:
  - `useUserSwitchListener()`: General purpose listener
  - `useDataRefreshOnUserSwitch()`: Auto-refresh data on switch
  - `useUIUpdateOnUserSwitch()`: Update UI on switch

### 5. Enhanced Sidebar (`src/layouts/sidebar/index.jsx`)

- **Purpose**: Main UI for user switching
- **Key Features**:
  - User dropdown with available users
  - Visual indicators for current/original user
  - Proper state management during switching

## How It Works

### API Header Management

```javascript
// All API calls automatically include these headers:
{
  "Authorization": "Bearer <original_user_token>",
  "user": "<original_user_id>",           // For authorization
  "brand": "<switched_user_brand_id>"     // For data context
}
```

### User Switching Flow

1. User A logs in (original user)
2. User A invites User B through User Management
3. User B accepts invitation
4. User A can now switch to User B from navbar dropdown
5. When switched to User B:
   - All API calls use User A's token and ID for auth
   - All API calls use User B's brand ID for data context
   - UI shows User B's data and profile
   - User A can switch back anytime

### Storage Management

```javascript
// localStorage always contains current context:
localStorage.getItem("UserId"); // Current user ID (A or B)
localStorage.getItem("BrandId"); // Current brand ID (A's or B's)

// sessionStorage contains user data:
sessionStorage.getItem("userData"); // Original user A data
sessionStorage.getItem("switchUserData"); // Switched user B data (if any)
```

## Testing the Implementation

### 1. Basic User Switching Test

```javascript
// Test switching between users
import {
  switchToUser,
  getCurrentUserContext,
} from "./src/helpers/utils/userSwitchUtils";

// Get current context
const context = getCurrentUserContext();
console.log("Current user:", context.currentUser.name);
console.log("Is using switched user:", context.isUsingSwitchedUser);

// Switch to another user
const success = await switchToUser(targetUser);
console.log("Switch successful:", success);
```

### 2. API Header Test

```javascript
// Make any API call and check headers in browser dev tools
import apiInstance from "./src/helpers/Axios/axiosINstance";

// This will automatically include correct headers
const response = await apiInstance.get("/any-endpoint");
```

### 3. Event Listener Test

```javascript
// Listen for user switch events
window.addEventListener("userSwitched", (event) => {
  console.log("User switched:", event.detail);
});
```

### 4. Component Integration Test

```javascript
// Use in any component
import { useDataRefreshOnUserSwitch } from "./src/helpers/hooks/useUserSwitchListener";

const MyComponent = () => {
  const refreshData = (eventData) => {
    console.log("Refreshing data for new user:", eventData.newUser.name);
    // Refresh component data here
  };

  useDataRefreshOnUserSwitch(refreshData);

  return <div>My Component</div>;
};
```

## Integration Steps

### 1. Add to App.jsx

```javascript
import { UserProvider } from "./src/helpers/context/UserContext";

function App() {
  return <UserProvider>{/* Your app components */}</UserProvider>;
}
```

### 2. Use in Components

```javascript
import { useDataRefreshOnUserSwitch } from "./src/helpers/hooks/useUserSwitchListener";
import { getCurrentUserContext } from "./src/helpers/utils/userSwitchUtils";

const MyComponent = () => {
  const [data, setData] = useState([]);

  // Refresh data when user switches
  useDataRefreshOnUserSwitch(async () => {
    const userContext = getCurrentUserContext();
    // Fetch data for current user context
    const newData = await fetchDataForUser(userContext);
    setData(newData);
  });

  // Component render logic
};
```

### 3. Add User Switching to Navbar

The sidebar already includes the user switching dropdown. To add it elsewhere:

```javascript
import {
  switchToUser,
  fetchAvailableUsers,
} from "./src/helpers/utils/userSwitchUtils";

const UserSwitcher = () => {
  const [users, setUsers] = useState([]);

  useEffect(() => {
    fetchAvailableUsers().then(setUsers);
  }, []);

  return (
    <select
      onChange={(e) => {
        const user = users.find((u) => u.id === e.target.value);
        switchToUser(user);
      }}
    >
      {users.map((user) => (
        <option key={user.id} value={user.id}>
          {user.name}
        </option>
      ))}
    </select>
  );
};
```

## Troubleshooting

### Common Issues

1. **Headers not updating**: Check axios interceptor is properly configured
2. **User data not refreshing**: Ensure components use event listeners
3. **Storage inconsistency**: Verify localStorage/sessionStorage updates
4. **API authorization errors**: Check original user token is valid

### Debug Tools

1. Check browser dev tools Network tab for API headers
2. Use `getCurrentUserContext()` to inspect current state
3. Listen to `userSwitched` events in console
4. Use the provided `UserSwitchExample` component for testing

## Security Considerations

- Original user's token is always used for authentication
- Switched user can only access data they have permission for
- Brand-level data isolation is maintained
- No sensitive data is exposed in localStorage

This implementation provides a robust, Instagram-like multi-user experience with proper data isolation and security.

## IMPORTANT: Complete Implementation Status

✅ **COMPLETED FEATURES:**

1. **Enhanced Axios Interceptor** - Automatically handles headers for all API calls
2. **Global Data Refresh System** - Refreshes all user data when switching
3. **Auto Page Refresh Strategy** - Automatically refreshes pages/components
4. **Updated Key Components** - Dashboard, Profile, Analytics now refresh on user switch
5. **Event-Driven Architecture** - Components listen for user switch events
6. **Comprehensive Utilities** - Helper functions and hooks for easy integration

## How to Test the Complete Implementation

### Step 1: Verify Auto Refresh is Working

1. Open browser developer console
2. Switch users from the navbar dropdown
3. You should see console logs like:
   ```
   🚀 Initializing user switch auto refresh system
   👤 User switch detected, triggering global refresh
   🔄 Hybrid refresh for path: /dashboard
   🏠 Dashboard refreshing data due to user switch
   ```

### Step 2: Test Different Pages

1. **Dashboard** - Should reload automatically and show new user's data
2. **Profile** - Should reload and show switched user's profile
3. **Analytics** - Should reset and show switched user's analytics
4. **Planner** - Should reload with switched user's scheduled posts

### Step 3: Verify API Headers

1. Open Network tab in browser dev tools
2. Switch users
3. Make any API call (navigate to different page)
4. Check request headers should show:
   - `user: <original_user_id>` (for authorization)
   - `brand: <switched_user_brand_id>` (for data context)

### Step 4: Test Data Isolation

1. Switch to User B
2. Verify you see User B's data (posts, analytics, etc.)
3. Switch back to original user
4. Verify you see original user's data again

## Troubleshooting

### If Data Doesn't Refresh:

1. Check browser console for error messages
2. Verify the auto refresh system is initialized (look for 🚀 log)
3. Check if user switch events are firing (look for 👤 logs)
4. Ensure components have event listeners (look for 🔄 logs)

### If API Headers Are Wrong:

1. Check axios interceptor is working
2. Verify localStorage has correct UserId and BrandId
3. Check sessionStorage has correct userData and switchUserData

### If Page Doesn't Reload:

1. Check if current route is in reloadRoutes array
2. Verify auto refresh system is initialized
3. Check browser console for any JavaScript errors

## Quick Fix for Any Component

If any component is not refreshing properly, add this to it:

```javascript
import { useUserSwitchRefresh } from "../components/UserSwitchDataRefresher";

const MyComponent = () => {
  const [data, setData] = useState([]);

  // Add this hook to automatically refresh data on user switch
  useUserSwitchRefresh(
    async () => {
      console.log("Refreshing MyComponent data");
      // Your data fetching logic here
      const newData = await fetchMyData();
      setData(newData);
    },
    { debug: true }
  );

  // Rest of your component
};
```

## Current Status: READY FOR TESTING

The implementation is now complete and should work exactly like Instagram's multi-user functionality:

1. ✅ User switching from navbar dropdown
2. ✅ Automatic data refresh on switch
3. ✅ Proper API headers for authorization and data context
4. ✅ Page/component refresh strategies
5. ✅ Event-driven architecture for real-time updates
6. ✅ Comprehensive error handling and debugging

**Next Steps:**

1. Test the implementation thoroughly
2. Report any issues found during testing
3. Fine-tune refresh strategies if needed
