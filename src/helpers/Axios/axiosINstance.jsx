import axios from "axios";
import { clearStorage, fetchFromStorage } from "../context/storage";
import siteConstant from "../constant/siteConstant";

let isRedirecting = false;
const apiInstance = axios.create({
  baseURL: "https://api.flowkar.com/api",
  withCredentials: true,
});

// Helper function to get current user context data
const getCurrentUserContext = () => {
  // Get original user data (the user who initially logged in)
  const originalUserData = fetchFromStorage(
    siteConstant?.INDENTIFIERS?.USERDATA
  );

  // Get switched user data (if any user has been switched)
  const switchedUserData = fetchFromStorage(
    siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA
  );

  // Determine current active user (switched user takes precedence)
  const currentUser = switchedUserData || originalUserData;

  // For API headers, we need:
  // 1. Original user's ID for authorization (who has the permission)
  // 2. Current user's brand ID for data context (whose data to show)

  // Always use original user's ID for authorization
  const authUserId = originalUserData?.user_id;

  // Use switched user's brand ID if available, otherwise original user's brand ID
  const currentBrandId =
    switchedUserData?.brands?.[0]?.id ||
    switchedUserData?.brand_id ||
    originalUserData?.brand_id ||
    localStorage.getItem("BrandId");

  // Current user ID for display purposes
  const currentUserId = currentUser?.user_id || currentUser?.id;

  return {
    token: originalUserData?.token, // Always use original user's token for authentication
    authUserId, // Original user ID for authorization
    currentUserId, // Current active user ID
    currentBrandId, // Current brand ID (from switched user or original user)
    originalUserData,
    switchedUserData,
    isUsingSwitchedUser: !!switchedUserData,
  };
};

apiInstance.interceptors.request.use((config) => {
  const {
    token,
    authUserId,
    currentBrandId,
    isUsingSwitchedUser,
    originalUserData,
    switchedUserData,
  } = getCurrentUserContext();

  const clonedConfig = config;

  if (token && authUserId) {
    // Set headers for user switching functionality
    clonedConfig.headers = {
      Authorization: `Bearer ${token}`, // Original user's token for authentication
      user: authUserId, // Always use original user's ID for authorization
      // Add brand header if available and not already set
      ...(currentBrandId &&
        !clonedConfig.headers.brand && {
          brand: currentBrandId, // Use switched user's brand ID or original user's brand ID
        }),
      ...clonedConfig.headers,
      "Content-Type":
        clonedConfig.headers["Content-Type"] || "multipart/form-data",
    };

    // Debug logging for user switching (can be removed in production)
    if (isUsingSwitchedUser) {
      console.log("API Request with switched user context:", {
        authUserId,
        currentBrandId,
        switchedUser: switchedUserData?.name,
        originalUser: originalUserData?.name,
        endpoint: config.url,
      });
    }
  } else {
    clonedConfig.headers = {
      "Content-Type": "multipart/form-data",
      ...clonedConfig.headers,
    };
  }

  return clonedConfig;
});

apiInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      const { status } = error.response;
      if (status >= 500 && status <= 599) {
        console.error(`Server Error ${status}: ${error.response.statusText}`);
        window.location.href = "/bad-gateway";
      } else if (status === 403) {
        console.error("Forbidden: Unauthorized access");
        window.location.href = "/PageNotFound";
        clearStorage();
      } else if (status === 400) {
        console.error("Error:", error.response.data.message);
      }
    }
    return Promise.reject(error.response?.data ? error.response.data : error);
  }
);

export default apiInstance;
