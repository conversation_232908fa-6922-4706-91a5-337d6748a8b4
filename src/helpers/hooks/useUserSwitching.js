import { useCallback } from "react";
import { useUser } from "../context/UserContext";

/**
 * Custom hook for user switching functionality
 * Provides easy-to-use methods for switching between users
 */
export const useUserSwitching = () => {
  const {
    originalUserData,
    switchedUserData,
    currentUser,
    currentUserId,
    currentBrandId,
    isUsingSwitchedUser,
    availableUsers,
    isLoadingSwitch,
    isLoadingUsers,
    switchToUser,
    fetchAvailableUsers,
  } = useUser();

  /**
   * Switch to a specific user by ID
   * @param {string|number} userId - The ID of the user to switch to
   * @returns {Promise<boolean>} - Success status
   */
  const switchToUserById = useCallback(async (userId) => {
    const targetUser = availableUsers.find(
      user => user.id === userId || user.user_id === userId
    );
    
    if (!targetUser) {
      console.error(`User with ID ${userId} not found in available users`);
      return false;
    }
    
    return await switchToUser(targetUser);
  }, [availableUsers, switchToUser]);

  /**
   * Switch back to the original user
   * @returns {Promise<boolean>} - Success status
   */
  const switchToOriginalUser = useCallback(async () => {
    if (!originalUserData) {
      console.error("No original user data available");
      return false;
    }
    
    return await switchToUser({
      ...originalUserData,
      isOriginalUser: true,
    });
  }, [originalUserData, switchToUser]);

  /**
   * Get user display information for UI components
   * @param {Object} user - User object
   * @returns {Object} - Display information
   */
  const getUserDisplayInfo = useCallback((user) => {
    if (!user) return null;
    
    return {
      id: user.user_id || user.id,
      name: user.name || 'Unknown User',
      email: user.email || '',
      profileImage: user.profile_image || null,
      brandId: user.brands?.[0]?.id || user.brand_id,
      isOriginalUser: user.isOriginalUser || false,
    };
  }, []);

  /**
   * Get formatted list of available users for dropdown/selection components
   * @returns {Array} - Formatted user list
   */
  const getFormattedUserList = useCallback(() => {
    return availableUsers.map(user => ({
      ...getUserDisplayInfo(user),
      isCurrentUser: (user.user_id || user.id) === currentUserId,
      label: user.name || user.email || 'Unknown User',
      value: user.user_id || user.id,
    }));
  }, [availableUsers, currentUserId, getUserDisplayInfo]);

  /**
   * Check if a specific user is currently active
   * @param {string|number} userId - User ID to check
   * @returns {boolean} - Whether the user is currently active
   */
  const isUserActive = useCallback((userId) => {
    return (currentUserId?.toString() === userId?.toString());
  }, [currentUserId]);

  /**
   * Get the current user's display information
   * @returns {Object} - Current user display info
   */
  const getCurrentUserDisplayInfo = useCallback(() => {
    return getUserDisplayInfo(currentUser);
  }, [currentUser, getUserDisplayInfo]);

  /**
   * Refresh the list of available users
   * @returns {Promise<void>}
   */
  const refreshAvailableUsers = useCallback(async () => {
    await fetchAvailableUsers();
  }, [fetchAvailableUsers]);

  return {
    // User data
    originalUserData,
    switchedUserData,
    currentUser,
    currentUserId,
    currentBrandId,
    isUsingSwitchedUser,
    
    // Available users
    availableUsers,
    formattedUserList: getFormattedUserList(),
    
    // Loading states
    isLoadingSwitch,
    isLoadingUsers,
    
    // Actions
    switchToUser,
    switchToUserById,
    switchToOriginalUser,
    refreshAvailableUsers,
    
    // Utility functions
    getUserDisplayInfo,
    getCurrentUserDisplayInfo,
    isUserActive,
  };
};

export default useUserSwitching;
