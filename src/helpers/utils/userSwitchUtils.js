/**
 * Utility functions for user switching functionality
 * These functions help maintain consistency across the application
 */

import { fetchFromStorage, saveToStorage } from "../context/storage";
import siteConstant from "../constant/siteConstant";
import apiInstance from "../Axios/axiosINstance";
import { URL } from "../constant/Url";

/**
 * Get the current user context with all necessary information
 */
export const getCurrentUserContext = () => {
  const originalUserData = fetchFromStorage(
    siteConstant?.INDENTIFIERS?.USERDATA
  );
  const switchedUserData = fetchFromStorage(
    siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA
  );
  const currentUser = switchedUserData || originalUserData;

  return {
    originalUserData,
    switchedUserData,
    currentUser,
    isUsingSwitchedUser: !!switchedUserData,
    currentUserId: currentUser?.user_id || currentUser?.id,
    currentBrandId:
      switchedUserData?.brands?.[0]?.id ||
      switchedUserData?.brand_id ||
      originalUserData?.brand_id ||
      localStorage.getItem("BrandId"),
    authUserId: originalUserData?.user_id, // Always use original user for auth
    token: originalUserData?.token,
  };
};

/**
 * Switch to a specific user
 * @param {Object} targetUser - The user to switch to
 * @returns {Promise<boolean>} - Success status
 */
export const switchToUser = async (targetUser) => {
  const { originalUserData } = getCurrentUserContext();

  if (!originalUserData) {
    console.error("No original user data available for switching");
    return false;
  }

  try {
    console.log("Switching to user:", targetUser?.name);

    // Check if switching back to original user
    const isOriginalUser =
      targetUser.id === originalUserData.user_id ||
      targetUser.user_id === originalUserData.user_id ||
      targetUser.isOriginalUser;

    let newBrandId;

    if (isOriginalUser) {
      // Switch back to original user
      console.log("Switching back to original user");
      saveToStorage(siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA, null);

      // Restore original user's localStorage values
      localStorage.setItem(
        "UserId",
        originalUserData.user_id?.toString() || ""
      );
      localStorage.setItem(
        "BrandId",
        originalUserData.brand_id?.toString() || ""
      );
      newBrandId = originalUserData.brand_id;
    } else {
      // Switch to different user
      console.log("Switching to different user:", targetUser.name);
      saveToStorage(siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA, targetUser);

      // Update localStorage with switched user's values
      const userBrandId = targetUser.brands?.[0]?.id || targetUser.brand_id;
      localStorage.setItem(
        "UserId",
        (targetUser.user_id || targetUser.id)?.toString() || ""
      );
      if (userBrandId) {
        localStorage.setItem("BrandId", userBrandId.toString());
        newBrandId = userBrandId;
      }
    }

    // Call the switch user API to validate the switch
    const apiCallBrandId = newBrandId;

    console.log("Making API call with headers:", {
      brand: apiCallBrandId,
      user: originalUserData.user_id,
    });

    const response = await apiInstance.get(URL.All_USERS, {
      headers: {
        brand: apiCallBrandId,
        user: originalUserData.user_id, // Always use original user for authorization
      },
    });

    if (response?.data?.status) {
      console.log("User switch API call successful");

      // Dispatch custom event to notify other components
      window.dispatchEvent(
        new CustomEvent("userSwitched", {
          detail: {
            previousUser: getCurrentUserContext().currentUser,
            newUser: isOriginalUser ? originalUserData : targetUser,
            isOriginalUser,
            brandId: newBrandId,
            timestamp: Date.now(),
          },
        })
      );

      return true;
    }

    console.error("User switch API call failed");
    return false;
  } catch (error) {
    console.error("Failed to switch user:", error);
    return false;
  }
};

/**
 * Fetch available users for switching
 * @returns {Promise<Array>} - List of available users
 */
export const fetchAvailableUsers = async () => {
  const { originalUserData, currentBrandId } = getCurrentUserContext();

  if (!originalUserData) {
    console.error("No original user data available");
    return [];
  }

  try {
    const response = await apiInstance.get(URL.GET_INVITEE_USERS, {
      headers: {
        brand: currentBrandId,
        user: originalUserData.user_id,
      },
    });

    const users = response?.data?.data ?? [];
    const usersList = Array.isArray(users) ? users : [];

    // Always add the original user to the list if not present
    const originalUserExists = usersList.some(
      (user) =>
        user.id === originalUserData.user_id ||
        user.user_id === originalUserData.user_id
    );

    if (!originalUserExists) {
      const originalUser = {
        id: originalUserData.user_id,
        user_id: originalUserData.user_id,
        name: originalUserData.name,
        email: originalUserData.email,
        profile_image: originalUserData.profile_image,
        brand_id: originalUserData.brand_id,
        brands: originalUserData.brands || [],
        isOriginalUser: true,
      };
      usersList.unshift(originalUser);
    }

    return usersList;
  } catch (error) {
    console.error("Failed to fetch available users:", error);
    return [];
  }
};

/**
 * Check if a user is currently active
 * @param {string|number} userId - User ID to check
 * @returns {boolean} - Whether the user is currently active
 */
export const isUserActive = (userId) => {
  const { currentUserId } = getCurrentUserContext();
  return currentUserId?.toString() === userId?.toString();
};

/**
 * Get display information for a user
 * @param {Object} user - User object
 * @returns {Object} - Display information
 */
export const getUserDisplayInfo = (user) => {
  if (!user) return null;

  return {
    id: user.user_id || user.id,
    name: user.name,
    email: user.email,
    profileImage: user.profile_image,
    brandId: user.brands?.[0]?.id || user.brand_id,
    isOriginalUser: user.isOriginalUser || false,
  };
};

/**
 * Trigger a user switch event manually
 * @param {Object} eventDetail - Event details
 */
export const triggerUserSwitchEvent = (eventDetail) => {
  window.dispatchEvent(
    new CustomEvent("userSwitched", {
      detail: {
        timestamp: Date.now(),
        ...eventDetail,
      },
    })
  );
};

/**
 * Add a new user account (LOGIN_SWITCH API)
 * @param {Object} credentials - Login credentials
 * @returns {Promise<Object>} - API response
 */
export const addUserAccount = async (credentials) => {
  try {
    const form = new FormData();
    form.append("creds", credentials.email);
    form.append("password", credentials.password);

    const response = await apiInstance.post(URL.LOGIN_SWITCH, form);

    if (response?.data?.status && response?.data?.token) {
      // Save the new user data
      const newUserData = { ...response.data };
      delete newUserData.message;
      delete newUserData.status;

      saveToStorage(siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA, newUserData);

      // Update localStorage
      localStorage.setItem("UserId", newUserData.user_id?.toString() || "");
      if (newUserData.brand_id) {
        localStorage.setItem("BrandId", newUserData.brand_id.toString());
      }

      // Trigger user switch event
      triggerUserSwitchEvent({
        newUser: newUserData,
        isOriginalUser: false,
        reason: "newAccountAdded",
      });

      return { success: true, data: newUserData };
    }

    return {
      success: false,
      message: response?.data?.message || "Login failed",
    };
  } catch (error) {
    console.error("Failed to add user account:", error);
    return { success: false, message: error.message || "An error occurred" };
  }
};

export default {
  getCurrentUserContext,
  switchToUser,
  fetchAvailableUsers,
  isUserActive,
  getUserDisplayInfo,
  triggerUserSwitchEvent,
  addUserAccount,
};
