import React, { createContext, useContext, useState, useEffect, useCallback } from "react";
import { fetchFromStorage, saveToStorage } from "./storage";
import siteConstant from "../constant/siteConstant";
import apiInstance from "../Axios/axiosINstance";
import { URL } from "../constant/Url";

const UserContext = createContext();

export const UserProvider = ({ children }) => {
  // Original user data (the user who initially logged in)
  const [originalUserData, setOriginalUserData] = useState(
    fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA)
  );
  
  // Currently switched user data (null if using original user)
  const [switchedUserData, setSwitchedUserData] = useState(
    fetchFromStorage(siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA)
  );
  
  // List of available users that can be switched to
  const [availableUsers, setAvailableUsers] = useState([]);
  
  // Loading states
  const [isLoadingSwitch, setIsLoadingSwitch] = useState(false);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);

  // Computed current user (switched user takes precedence over original user)
  const currentUser = switchedUserData || originalUserData;
  
  // Computed current user ID (for API headers)
  const currentUserId = currentUser?.user_id || currentUser?.id;
  
  // Computed current brand ID (for API headers)
  const currentBrandId = 
    switchedUserData?.brands?.[0]?.id || 
    switchedUserData?.brand_id || 
    originalUserData?.brand_id ||
    localStorage.getItem("BrandId");

  // Check if currently using switched user
  const isUsingSwitchedUser = !!switchedUserData;

  // Update localStorage when user data changes
  useEffect(() => {
    if (currentUserId) {
      localStorage.setItem("UserId", currentUserId.toString());
    }
    if (currentBrandId) {
      localStorage.setItem("BrandId", currentBrandId.toString());
    }
  }, [currentUserId, currentBrandId]);

  // Fetch available users for switching
  const fetchAvailableUsers = useCallback(async () => {
    if (!originalUserData?.user_id) return;
    
    setIsLoadingUsers(true);
    try {
      const response = await apiInstance.get(URL.GET_INVITEE_USERS, {
        headers: {
          brand: currentBrandId,
          user: originalUserData.user_id, // Always use original user for authorization
        },
      });
      
      const users = response?.data?.data ?? [];
      const usersList = Array.isArray(users) ? users : [];

      // Always add the original user to the list
      const originalUserExists = usersList.some(
        (user) => user.id === originalUserData.user_id || user.user_id === originalUserData.user_id
      );

      if (!originalUserExists) {
        const originalUser = {
          id: originalUserData.user_id,
          user_id: originalUserData.user_id,
          name: originalUserData.name,
          email: originalUserData.email,
          profile_image: originalUserData.profile_image,
          brand_id: originalUserData.brand_id,
          brands: originalUserData.brands || [],
          isOriginalUser: true,
        };
        usersList.unshift(originalUser);
      }

      setAvailableUsers(usersList);
    } catch (error) {
      console.error("Failed to fetch available users:", error);
      setAvailableUsers([]);
    } finally {
      setIsLoadingUsers(false);
    }
  }, [originalUserData?.user_id, currentBrandId]);

  // Switch to a different user
  const switchToUser = useCallback(async (targetUser) => {
    if (!originalUserData) return false;
    
    setIsLoadingSwitch(true);
    try {
      // Check if switching back to original user
      const isOriginalUser = 
        targetUser.id === originalUserData.user_id || 
        targetUser.user_id === originalUserData.user_id ||
        targetUser.isOriginalUser;

      if (isOriginalUser) {
        // Switch back to original user
        setSwitchedUserData(null);
        saveToStorage(siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA, null);
        
        // Restore original user's localStorage values
        localStorage.setItem("UserId", originalUserData.user_id?.toString() || "");
        localStorage.setItem("BrandId", originalUserData.brand_id?.toString() || "");
      } else {
        // Switch to different user
        setSwitchedUserData(targetUser);
        saveToStorage(siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA, targetUser);
        
        // Update localStorage with switched user's values
        const userBrandId = targetUser.brands?.[0]?.id || targetUser.brand_id;
        localStorage.setItem("UserId", (targetUser.user_id || targetUser.id)?.toString() || "");
        if (userBrandId) {
          localStorage.setItem("BrandId", userBrandId.toString());
        }
      }

      // Call the switch user API
      const apiCallBrandId = isOriginalUser
        ? originalUserData.brand_id
        : targetUser.brands?.[0]?.id || targetUser.brand_id || currentBrandId;

      const response = await apiInstance.get(URL.All_USERS, {
        headers: {
          brand: apiCallBrandId,
          user: originalUserData.user_id, // Always use original user for authorization
        },
      });

      if (response?.data?.status) {
        // Dispatch custom event to notify other components
        window.dispatchEvent(new CustomEvent("userSwitched", {
          detail: {
            previousUser: currentUser,
            newUser: isOriginalUser ? originalUserData : targetUser,
            isOriginalUser,
          }
        }));
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error("Failed to switch user:", error);
      // Revert changes on error
      setSwitchedUserData(fetchFromStorage(siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA));
      return false;
    } finally {
      setIsLoadingSwitch(false);
    }
  }, [originalUserData, currentUser, currentBrandId]);

  // Update original user data (when user logs in/out)
  const updateOriginalUserData = useCallback((userData) => {
    setOriginalUserData(userData);
    if (userData) {
      saveToStorage(siteConstant?.INDENTIFIERS?.USERDATA, userData);
    } else {
      // User logged out - clear everything
      setSwitchedUserData(null);
      saveToStorage(siteConstant?.INDENTIFIERS?.USERDATA, null);
      saveToStorage(siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA, null);
      localStorage.removeItem("UserId");
      localStorage.removeItem("BrandId");
    }
  }, []);

  // Initialize available users when original user data is available
  useEffect(() => {
    if (originalUserData?.user_id) {
      fetchAvailableUsers();
    }
  }, [originalUserData?.user_id, fetchAvailableUsers]);

  const contextValue = {
    // User data
    originalUserData,
    switchedUserData,
    currentUser,
    currentUserId,
    currentBrandId,
    isUsingSwitchedUser,
    
    // Available users
    availableUsers,
    
    // Loading states
    isLoadingSwitch,
    isLoadingUsers,
    
    // Actions
    switchToUser,
    fetchAvailableUsers,
    updateOriginalUserData,
  };

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};

export default UserContext;
