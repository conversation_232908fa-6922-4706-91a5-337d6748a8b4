import React, { useState, useEffect } from 'react';
import { useDataRefreshOnUserSwitch } from '../helpers/hooks/useUserSwitchListener';
import { getCurrentUserContext, fetchAvailableUsers, switchToUser } from '../helpers/utils/userSwitchUtils';

/**
 * Example component showing how to implement user switching functionality
 * This demonstrates the proper way to handle user switching in any component
 */
const UserSwitchExample = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentUserContext, setCurrentUserContext] = useState(null);

  // Refresh data when user switches
  const refreshData = async (eventData) => {
    console.log('Refreshing data due to user switch:', eventData);
    setLoading(true);
    
    try {
      // Update current user context
      const userContext = getCurrentUserContext();
      setCurrentUserContext(userContext);
      
      // Fetch fresh data for the new user
      const availableUsers = await fetchAvailableUsers();
      setUsers(availableUsers);
      
      console.log('Data refreshed successfully');
    } catch (error) {
      console.error('Failed to refresh data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Use the hook to listen for user switch events
  useDataRefreshOnUserSwitch(refreshData);

  // Initial data load
  useEffect(() => {
    const loadInitialData = async () => {
      setLoading(true);
      try {
        const userContext = getCurrentUserContext();
        setCurrentUserContext(userContext);
        
        const availableUsers = await fetchAvailableUsers();
        setUsers(availableUsers);
      } catch (error) {
        console.error('Failed to load initial data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // Handle user switch
  const handleUserSwitch = async (user) => {
    setLoading(true);
    try {
      const success = await switchToUser(user);
      if (success) {
        console.log('User switched successfully');
        // The useDataRefreshOnUserSwitch hook will automatically refresh data
      } else {
        console.error('Failed to switch user');
      }
    } catch (error) {
      console.error('Error switching user:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div style={{ padding: '20px' }}>
      <h2>User Switching Example</h2>
      
      {/* Current User Info */}
      <div style={{ marginBottom: '20px', padding: '10px', border: '1px solid #ccc' }}>
        <h3>Current User</h3>
        {currentUserContext ? (
          <div>
            <p><strong>Name:</strong> {currentUserContext.currentUser?.name}</p>
            <p><strong>Email:</strong> {currentUserContext.currentUser?.email}</p>
            <p><strong>User ID:</strong> {currentUserContext.currentUserId}</p>
            <p><strong>Brand ID:</strong> {currentUserContext.currentBrandId}</p>
            <p><strong>Using Switched User:</strong> {currentUserContext.isUsingSwitchedUser ? 'Yes' : 'No'}</p>
            {currentUserContext.isUsingSwitchedUser && (
              <p><strong>Original User:</strong> {currentUserContext.originalUserData?.name}</p>
            )}
          </div>
        ) : (
          <p>No user data available</p>
        )}
      </div>

      {/* Available Users */}
      <div>
        <h3>Available Users</h3>
        {users.length > 0 ? (
          <div style={{ display: 'grid', gap: '10px' }}>
            {users.map((user) => {
              const isActive = currentUserContext?.currentUserId === (user.user_id || user.id);
              
              return (
                <div
                  key={user.id || user.user_id}
                  style={{
                    padding: '10px',
                    border: '1px solid #ddd',
                    borderRadius: '5px',
                    backgroundColor: isActive ? '#e3f2fd' : 'white',
                    cursor: 'pointer'
                  }}
                  onClick={() => !isActive && handleUserSwitch(user)}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                    {user.profile_image && (
                      <img
                        src={user.profile_image}
                        alt={user.name}
                        style={{
                          width: '40px',
                          height: '40px',
                          borderRadius: '50%',
                          objectFit: 'cover'
                        }}
                      />
                    )}
                    <div>
                      <p style={{ margin: 0, fontWeight: 'bold' }}>
                        {user.name}
                        {user.isOriginalUser && <span style={{ color: '#4caf50' }}> (YOU)</span>}
                        {isActive && <span style={{ color: '#2196f3' }}> (ACTIVE)</span>}
                      </p>
                      <p style={{ margin: 0, color: '#666', fontSize: '14px' }}>
                        {user.email}
                      </p>
                      <p style={{ margin: 0, color: '#999', fontSize: '12px' }}>
                        ID: {user.user_id || user.id} | Brand: {user.brand_id || user.brands?.[0]?.id}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <p>No users available</p>
        )}
      </div>

      {/* Debug Info */}
      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f5f5f5' }}>
        <h4>Debug Information</h4>
        <pre style={{ fontSize: '12px', overflow: 'auto' }}>
          {JSON.stringify(currentUserContext, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default UserSwitchExample;
