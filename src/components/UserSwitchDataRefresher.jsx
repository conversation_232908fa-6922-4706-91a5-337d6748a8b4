import { useEffect } from 'react';

/**
 * UserSwitchDataRefresher Component
 * 
 * This is a utility component that can be added to any page/component
 * to automatically refresh data when user switching occurs.
 * 
 * Usage:
 * <UserSwitchDataRefresher onRefresh={refreshMyData} />
 * 
 * Or for automatic page reload:
 * <UserSwitchDataRefresher autoReload={true} />
 */
const UserSwitchDataRefresher = ({ 
  onRefresh, 
  autoReload = false, 
  delay = 200,
  debug = false 
}) => {
  useEffect(() => {
    const handleUserSwitch = (event) => {
      if (debug) {
        console.log('🔄 UserSwitchDataRefresher: User switch detected', event.detail);
      }

      if (autoReload) {
        // Simple page reload approach
        setTimeout(() => {
          if (debug) {
            console.log('🔄 UserSwitchDataRefresher: Reloading page');
          }
          window.location.reload();
        }, delay);
      } else if (onRefresh && typeof onRefresh === 'function') {
        // Custom refresh function approach
        setTimeout(() => {
          if (debug) {
            console.log('🔄 UserSwitchDataRefresher: Calling custom refresh function');
          }
          onRefresh(event.detail);
        }, delay);
      }
    };

    const handleGlobalRefresh = (event) => {
      if (debug) {
        console.log('🔄 UserSwitchDataRefresher: Global refresh detected', event.detail);
      }

      if (onRefresh && typeof onRefresh === 'function') {
        setTimeout(() => {
          onRefresh(event.detail);
        }, delay);
      }
    };

    // Listen for user switch events
    window.addEventListener('userSwitched', handleUserSwitch);
    window.addEventListener('globalDataRefresh', handleGlobalRefresh);

    return () => {
      window.removeEventListener('userSwitched', handleUserSwitch);
      window.removeEventListener('globalDataRefresh', handleGlobalRefresh);
    };
  }, [onRefresh, autoReload, delay, debug]);

  // This component doesn't render anything
  return null;
};

/**
 * Hook version of the UserSwitchDataRefresher
 * Use this in functional components
 */
export const useUserSwitchRefresh = (refreshFunction, options = {}) => {
  const { 
    autoReload = false, 
    delay = 200, 
    debug = false,
    refreshOnMount = true 
  } = options;

  useEffect(() => {
    // Refresh on mount if requested
    if (refreshOnMount && refreshFunction && typeof refreshFunction === 'function') {
      if (debug) {
        console.log('🔄 useUserSwitchRefresh: Refreshing on mount');
      }
      refreshFunction({ reason: 'mount', timestamp: Date.now() });
    }
  }, []);

  useEffect(() => {
    const handleUserSwitch = (event) => {
      if (debug) {
        console.log('🔄 useUserSwitchRefresh: User switch detected', event.detail);
      }

      if (autoReload) {
        setTimeout(() => {
          if (debug) {
            console.log('🔄 useUserSwitchRefresh: Reloading page');
          }
          window.location.reload();
        }, delay);
      } else if (refreshFunction && typeof refreshFunction === 'function') {
        setTimeout(() => {
          if (debug) {
            console.log('🔄 useUserSwitchRefresh: Calling refresh function');
          }
          refreshFunction(event.detail);
        }, delay);
      }
    };

    const handleGlobalRefresh = (event) => {
      if (debug) {
        console.log('🔄 useUserSwitchRefresh: Global refresh detected', event.detail);
      }

      if (refreshFunction && typeof refreshFunction === 'function') {
        setTimeout(() => {
          refreshFunction(event.detail);
        }, delay);
      }
    };

    // Listen for events
    window.addEventListener('userSwitched', handleUserSwitch);
    window.addEventListener('globalDataRefresh', handleGlobalRefresh);

    return () => {
      window.removeEventListener('userSwitched', handleUserSwitch);
      window.removeEventListener('globalDataRefresh', handleGlobalRefresh);
    };
  }, [refreshFunction, autoReload, delay, debug]);
};

/**
 * Higher-Order Component that adds user switch refresh functionality
 * to any component
 */
export const withUserSwitchRefresh = (WrappedComponent, refreshFunction) => {
  return function UserSwitchRefreshWrapper(props) {
    useUserSwitchRefresh(refreshFunction, { debug: true });
    return <WrappedComponent {...props} />;
  };
};

export default UserSwitchDataRefresher;
