import { useEffect, useCallback } from "react";
import { fetchFromStorage } from "../context/storage";
import siteConstant from "../constant/siteConstant";

/**
 * Custom hook to listen for user switching events and handle data refresh
 * This ensures all components stay in sync when users switch accounts
 */
export const useUserSwitchListener = (options = {}) => {
  const {
    onUserSwitch,
    refreshData = true,
    refreshOnMount = false,
    dependencies = [],
  } = options;

  // Get current user context
  const getCurrentUserContext = useCallback(() => {
    const originalUserData = fetchFromStorage(
      siteConstant?.INDENTIFIERS?.USERDATA
    );
    const switchedUserData = fetchFromStorage(
      siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA
    );
    const currentUser = switchedUserData || originalUserData;

    return {
      originalUserData,
      switchedUserData,
      currentUser,
      isUsingSwitchedUser: !!switchedUserData,
      currentUserId: currentUser?.user_id || currentUser?.id,
      currentBrandId:
        switchedUserData?.brands?.[0]?.id ||
        switchedUserData?.brand_id ||
        originalUserData?.brand_id ||
        localStorage.getItem("BrandId"),
    };
  }, []);

  // Handle user switch event
  const handleUserSwitch = useCallback(
    (event) => {
      console.log("User switch event detected:", event.detail);

      const userContext = getCurrentUserContext();

      // Call custom handler if provided
      if (onUserSwitch && typeof onUserSwitch === "function") {
        onUserSwitch({
          ...event.detail,
          userContext,
        });
      }

      // Auto-refresh data if enabled
      if (refreshData) {
        // Dispatch a custom event for components that need to refresh their data
        window.dispatchEvent(
          new CustomEvent("refreshComponentData", {
            detail: {
              reason: "userSwitch",
              userContext,
              timestamp: Date.now(),
            },
          })
        );
      }
    },
    [onUserSwitch, refreshData, getCurrentUserContext]
  );

  // Set up event listeners
  useEffect(() => {
    // Listen for user switch events
    window.addEventListener("userSwitched", handleUserSwitch);

    // Cleanup
    return () => {
      window.removeEventListener("userSwitched", handleUserSwitch);
    };
  }, [handleUserSwitch, ...dependencies]);

  // Refresh on mount if requested
  useEffect(() => {
    if (refreshOnMount && onUserSwitch) {
      const userContext = getCurrentUserContext();
      onUserSwitch({
        reason: "mount",
        userContext,
        timestamp: Date.now(),
      });
    }
  }, [refreshOnMount, onUserSwitch, getCurrentUserContext]);

  return {
    getCurrentUserContext,
  };
};

/**
 * Hook specifically for components that need to refresh data when user switches
 */
export const useDataRefreshOnUserSwitch = (
  refreshCallback,
  dependencies = []
) => {
  return useUserSwitchListener({
    onUserSwitch: (eventData) => {
      console.log("Refreshing data due to user switch");
      if (refreshCallback && typeof refreshCallback === "function") {
        refreshCallback(eventData);
      }
    },
    refreshData: false, // We handle refresh manually
    dependencies,
  });
};

/**
 * Hook for components that need to update their UI when user switches
 */
export const useUIUpdateOnUserSwitch = (updateCallback, dependencies = []) => {
  return useUserSwitchListener({
    onUserSwitch: (eventData) => {
      console.log("Updating UI due to user switch");
      if (updateCallback && typeof updateCallback === "function") {
        updateCallback(eventData);
      }
    },
    refreshData: false,
    dependencies,
  });
};

/**
 * Utility function to manually trigger user switch event
 * Useful for components that perform user switching
 */
export const triggerUserSwitchEvent = (eventDetail) => {
  window.dispatchEvent(
    new CustomEvent("userSwitched", {
      detail: {
        timestamp: Date.now(),
        ...eventDetail,
      },
    })
  );
};

/**
 * Utility function to get current user context from anywhere in the app
 */
export const getCurrentUserContext = () => {
  const originalUserData = fetchFromStorage(
    siteConstant?.INDENTIFIERS?.USERDATA
  );
  const switchedUserData = fetchFromStorage(
    siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA
  );
  const currentUser = switchedUserData || originalUserData;

  return {
    originalUserData,
    switchedUserData,
    currentUser,
    isUsingSwitchedUser: !!switchedUserData,
    currentUserId: currentUser?.user_id || currentUser?.id,
    currentBrandId:
      switchedUserData?.brands?.[0]?.id ||
      switchedUserData?.brand_id ||
      originalUserData?.brand_id ||
      localStorage.getItem("BrandId"),
    authUserId: originalUserData?.user_id, // Always use original user for auth
  };
};

export default useUserSwitchListener;
