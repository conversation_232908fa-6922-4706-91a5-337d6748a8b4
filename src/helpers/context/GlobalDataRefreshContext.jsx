import React, { createContext, useContext, useCallback, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { fetchProfile } from '../../redux/slices/profileFetch';

/**
 * Global Data Refresh Context
 * This context manages data refresh across the entire application when user switching occurs
 */
const GlobalDataRefreshContext = createContext();

export const GlobalDataRefreshProvider = ({ children }) => {
  const dispatch = useDispatch();

  // Global refresh function that refreshes all user-specific data
  const refreshAllUserData = useCallback(async (eventData) => {
    console.log('🔄 Global data refresh initiated:', eventData);
    
    try {
      // 1. Refresh Redux profile data
      dispatch(fetchProfile());
      
      // 2. Trigger custom event for components to refresh their data
      window.dispatchEvent(new CustomEvent('globalDataRefresh', {
        detail: {
          reason: 'userSwitch',
          timestamp: Date.now(),
          ...eventData
        }
      }));
      
      // 3. Force page reload for components that don't listen to events
      // This is a fallback to ensure all data is refreshed
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('forceComponentRefresh', {
          detail: {
            reason: 'userSwitch',
            timestamp: Date.now()
          }
        }));
      }, 200);
      
      console.log('✅ Global data refresh completed');
    } catch (error) {
      console.error('❌ Error during global data refresh:', error);
    }
  }, [dispatch]);

  // Listen for user switch events
  useEffect(() => {
    const handleUserSwitch = (event) => {
      console.log('👤 User switch detected, triggering global refresh');
      refreshAllUserData(event.detail);
    };

    window.addEventListener('userSwitched', handleUserSwitch);
    
    return () => {
      window.removeEventListener('userSwitched', handleUserSwitch);
    };
  }, [refreshAllUserData]);

  const contextValue = {
    refreshAllUserData
  };

  return (
    <GlobalDataRefreshContext.Provider value={contextValue}>
      {children}
    </GlobalDataRefreshContext.Provider>
  );
};

export const useGlobalDataRefresh = () => {
  const context = useContext(GlobalDataRefreshContext);
  if (!context) {
    throw new Error('useGlobalDataRefresh must be used within a GlobalDataRefreshProvider');
  }
  return context;
};

/**
 * Hook for components to automatically refresh their data on user switch
 * This is the main hook that components should use
 */
export const useAutoRefreshOnUserSwitch = (refreshFunction, dependencies = []) => {
  useEffect(() => {
    if (!refreshFunction || typeof refreshFunction !== 'function') {
      return;
    }

    // Handle global data refresh events
    const handleGlobalRefresh = (event) => {
      console.log('🔄 Component refreshing data due to global refresh event');
      refreshFunction(event.detail);
    };

    // Handle force refresh events (fallback)
    const handleForceRefresh = (event) => {
      console.log('🔄 Component force refreshing data');
      refreshFunction(event.detail);
    };

    // Handle user switch events directly (primary)
    const handleUserSwitch = (event) => {
      console.log('🔄 Component refreshing data due to user switch');
      // Add delay to ensure localStorage is updated
      setTimeout(() => {
        refreshFunction(event.detail);
      }, 150);
    };

    // Listen to multiple events to ensure data refresh
    window.addEventListener('globalDataRefresh', handleGlobalRefresh);
    window.addEventListener('forceComponentRefresh', handleForceRefresh);
    window.addEventListener('userSwitched', handleUserSwitch);

    return () => {
      window.removeEventListener('globalDataRefresh', handleGlobalRefresh);
      window.removeEventListener('forceComponentRefresh', handleForceRefresh);
      window.removeEventListener('userSwitched', handleUserSwitch);
    };
  }, [refreshFunction, ...dependencies]);
};

/**
 * Hook for components that need to refresh data immediately when mounted
 * and also when user switches
 */
export const useRefreshDataOnMount = (refreshFunction, dependencies = []) => {
  // Refresh on mount
  useEffect(() => {
    if (refreshFunction && typeof refreshFunction === 'function') {
      console.log('🔄 Component refreshing data on mount');
      refreshFunction({ reason: 'mount', timestamp: Date.now() });
    }
  }, []);

  // Also use auto refresh on user switch
  useAutoRefreshOnUserSwitch(refreshFunction, dependencies);
};

/**
 * Utility function to manually trigger global data refresh
 */
export const triggerGlobalDataRefresh = (reason = 'manual') => {
  window.dispatchEvent(new CustomEvent('globalDataRefresh', {
    detail: {
      reason,
      timestamp: Date.now()
    }
  }));
};

export default GlobalDataRefreshContext;
