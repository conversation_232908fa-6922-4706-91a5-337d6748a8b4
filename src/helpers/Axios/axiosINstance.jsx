import axios from "axios";
import { clearStorage, fetchFromStorage } from "../context/storage";
import siteConstant from "../constant/siteConstant";

let isRedirecting = false;
const apiInstance = axios.create({
  baseURL: "https://api.flowkar.com/api",
  withCredentials: true,
});

// Helper function to get current user context data
const getCurrentUserContext = () => {
  // Get original user data
  const originalUserData = fetchFromStorage(
    siteConstant?.INDENTIFIERS?.USERDATA
  );

  // Get switched user data (if any)
  const switchedUserData = fetchFromStorage(
    siteConstant?.INDENTIFIERS?.SWITCH_USER_DATA
  );

  // Determine current user (switched user takes precedence)
  const currentUser = switchedUserData || originalUserData;

  // Get current user ID for headers
  const currentUserId = currentUser?.user_id || currentUser?.id;

  // Get current brand ID for headers
  const currentBrandId =
    switchedUserData?.brands?.[0]?.id ||
    switchedUserData?.brand_id ||
    originalUserData?.brand_id ||
    localStorage.getItem("BrandId");

  return {
    token: originalUserData?.token, // Always use original user's token for authentication
    currentUserId,
    currentBrandId,
    originalUserId: originalUserData?.user_id, // For authorization purposes
  };
};

apiInstance.interceptors.request.use((config) => {
  const { token, currentUserId, currentBrandId, originalUserId } =
    getCurrentUserContext();
  const clonedConfig = config;

  if (token) {
    clonedConfig.headers = {
      Authorization: `Bearer ${token}`,
      user: currentUserId || originalUserId, // Use current user ID, fallback to original
      // Add brand header if available and not already set
      ...(currentBrandId &&
        !clonedConfig.headers.brand && { brand: currentBrandId }),
      ...clonedConfig.headers,
      "Content-Type":
        clonedConfig.headers["Content-Type"] || "multipart/form-data",
    };
  } else {
    clonedConfig.headers = {
      "Content-Type": "multipart/form-data",
      ...clonedConfig.headers,
    };
  }
  return clonedConfig;
});

apiInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      const { status } = error.response;
      if (status >= 500 && status <= 599) {
        console.error(`Server Error ${status}: ${error.response.statusText}`);
        window.location.href = "/bad-gateway";
      } else if (status === 403) {
        console.error("Forbidden: Unauthorized access");
        window.location.href = "/PageNotFound";
        clearStorage();
      } else if (status === 400) {
        console.error("Error:", error.response.data.message);
      }
    }
    return Promise.reject(error.response?.data ? error.response.data : error);
  }
);

export default apiInstance;
